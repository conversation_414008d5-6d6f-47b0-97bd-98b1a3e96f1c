<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SSID Platform - 配置管理中心</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        :root { --bg-color: #f6f8fb; --sidebar-bg-color: #ffffff; --card-bg-color: #ffffff; --text-color: #1e293b; --text-color-secondary: #64748b; --border-color: #e5eaf3; --primary-color: #2d78f4; --primary-color-light: #f0f6ff; --shadow-color: rgba(0,0,0,0.06); --icon-color: #64748b; --icon-hover-bg: #f1f5f9; }
        [data-theme="dark"] { --bg-color: #111827; --sidebar-bg-color: #1f2937; --card-bg-color: #1f2937; --text-color: #f9fafb; --text-color-secondary: #9ca3af; --border-color: #374151; --primary-color: #3b82f6; --primary-color-light: #252e3d; --shadow-color: rgba(0,0,0,0.2); --icon-color: #9ca3af; --icon-hover-bg: #374151; }
        * { margin: 0; padding: 0; box-sizing: border-box; font-family: "PingFang SC", "Microsoft YaHei", sans-serif; }
        html, body { height: 100%; overflow: hidden; }
        body { background-color: var(--bg-color); color: var(--text-color); transition: background-color 0.3s, color 0.3s; display: flex; }
        .sidebar { width: 260px; background-color: var(--sidebar-bg-color); border-right: 1px solid var(--border-color); display: flex; flex-direction: column; height: 100%; flex-shrink: 0; transition: background-color 0.3s, border-color 0.3s; }
        .main-wrapper { flex-grow: 1; height: 100%; overflow-y: auto; }
        .main-content { padding: 24px; }
        .sidebar-header { display: flex; align-items: center; padding: 0 24px; height: 64px; border-bottom: 1px solid var(--border-color); flex-shrink: 0; }
        .sidebar-logo { font-size: 22px; font-weight: 700; color: var(--primary-color); }
        .sidebar-nav { flex-grow: 1; padding: 16px 0; }
        .nav-list { list-style: none; }
        .nav-item a { display: flex; align-items: center; gap: 12px; padding: 12px 24px; margin: 4px 16px; border-radius: 8px; color: var(--text-color-secondary); text-decoration: none; font-weight: 500; transition: background-color 0.2s, color 0.2s; }
        .nav-item a:hover { background-color: var(--primary-color-light); color: var(--primary-color); }
        .nav-item.active a { background-color: var(--primary-color-light); color: var(--primary-color); font-weight: 600; }
        .nav-icon { width: 20px; height: 20px; }
        .page-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 24px; }
        .page-title { font-size: 24px; font-weight: 700; }
        .header-actions { display: flex; align-items: center; gap: 16px; }
        .theme-toggle { background-color: var(--icon-hover-bg); border: none; border-radius: 50%; width: 36px; height: 36px; cursor: pointer; display: flex; align-items: center; justify-content: center; color: var(--icon-color); transition: background-color 0.2s; }
        .theme-toggle .icon { width: 20px; height: 20px; }
        #moon-icon { display: none; }
        .card { background-color: var(--card-bg-color); border-radius: 12px; margin-bottom: 24px; box-shadow: 0 4px 12px var(--shadow-color); border: 1px solid var(--border-color); overflow: hidden; transition: background-color 0.3s, border-color 0.3s; }
        .card-header { display: flex; justify-content: space-between; align-items: center; padding: 16px 24px; border-bottom: 1px solid var(--border-color); }
        .card-title { font-size: 17px; font-weight: 600; color: var(--text-color); }
        .card-content { padding: 24px; }
        .table { width: 100%; border-collapse: collapse; }
        .table th, .table td { text-align: left; padding: 12px 16px; border-bottom: 1px solid var(--border-color); font-size: 14px; }
        .table th { color: var(--text-color-secondary); font-weight: 500; }
        .btn { padding: 8px 16px; border-radius: 6px; border: none; font-size: 14px; cursor: pointer; font-weight: 500; transition: all 0.2s; }
        .btn-primary { background-color: var(--primary-color); color: white; }
        .btn-secondary { background-color: var(--icon-hover-bg); color: var(--text-color); border: 1px solid var(--border-color); }
        .btn-danger { background-color: rgba(255, 59, 48, 0.15); color: #ff3b30; }
        .action-buttons { display: flex; gap: 8px; }
        .badge { padding: 4px 10px; border-radius: 12px; font-size: 12px; font-weight: 500; }
        .badge-blue { background-color: rgba(45, 120, 244, 0.1); color: #2d78f4; }
        .badge-green { background-color: rgba(76, 217, 100, 0.1); color: #4cd964; }
        /* 保持原有样式，新增以下样式 */
        .channel-scenario-matrix { 
            display: grid; 
            gap: 16px; 
            margin-bottom: 24px; 
        }
        .matrix-header { 
            display: flex; 
            justify-content: space-between; 
            align-items: center; 
            margin-bottom: 16px; 
        }
        .matrix-view { 
            background: var(--card-bg-color); 
            border-radius: 8px; 
            overflow: hidden; 
            border: 1px solid var(--border-color); 
        }
        .matrix-table { 
            width: 100%; 
            border-collapse: collapse; 
        }
        .matrix-table th { 
            background: var(--primary-color-light); 
            padding: 12px; 
            text-align: center; 
            font-weight: 600; 
            border-right: 1px solid var(--border-color); 
        }
        .matrix-table td { 
            padding: 8px; 
            text-align: center; 
            border-right: 1px solid var(--border-color); 
            border-bottom: 1px solid var(--border-color); 
        }
        .channel-name { 
            font-weight: 600; 
            background: var(--primary-color-light); 
            text-align: left !important; 
            padding-left: 16px !important; 
        }
        .policy-cell { 
            position: relative; 
            min-height: 40px; 
            cursor: pointer; 
            transition: background-color 0.2s; 
        }
        .policy-cell:hover { 
            background: var(--primary-color-light); 
        }
        .policy-cell.has-policy { 
            background: rgba(76, 217, 100, 0.1); 
        }
        .policy-cell.has-policy:hover { 
            background: rgba(76, 217, 100, 0.2); 
        }
        .policy-indicator { 
            display: inline-block; 
            padding: 4px 8px; 
            border-radius: 4px; 
            font-size: 12px; 
            font-weight: 500; 
        }
        .policy-indicator.active { 
            background: #4cd964; 
            color: white; 
        }
        .policy-indicator.draft { 
            background: #ff9500; 
            color: white; 
        }
        .policy-indicator.none { 
            background: var(--border-color); 
            color: var(--text-color-secondary); 
        }
        .quick-actions { 
            display: flex; 
            gap: 8px; 
            margin-top: 4px; 
        }
        .quick-action-btn { 
            padding: 2px 6px; 
            font-size: 11px; 
            border-radius: 3px; 
            border: none; 
            cursor: pointer; 
            background: var(--primary-color); 
            color: white; 
            opacity: 0; 
            transition: opacity 0.2s; 
        }
        .policy-cell:hover .quick-action-btn { 
            opacity: 1; 
        }
        .legend { 
            display: flex; 
            gap: 16px; 
            margin-top: 12px; 
            font-size: 12px; 
        }
        .legend-item { 
            display: flex; 
            align-items: center; 
            gap: 6px; 
        }
        .legend-color { 
            width: 12px; 
            height: 12px; 
            border-radius: 2px; 
        }
        .policy-form-section { 
            margin-bottom: 32px; 
        }
        .section-header { 
            display: flex; 
            align-items: center; 
            gap: 8px; 
            margin-bottom: 16px; 
            padding-bottom: 8px; 
            border-bottom: 2px solid var(--primary-color); 
        }
        .section-icon { 
            width: 20px; 
            height: 20px; 
            color: var(--primary-color); 
        }
        .section-title { 
            font-size: 16px; 
            font-weight: 600; 
            color: var(--primary-color); 
        }
        .channel-scenario-selector { 
            background: var(--primary-color-light); 
            padding: 16px; 
            border-radius: 8px; 
            margin-bottom: 16px; 
        }
        .selector-title { 
            font-weight: 600; 
            margin-bottom: 12px; 
            color: var(--primary-color); 
        }
        .combination-display { 
            font-size: 18px; 
            font-weight: 600; 
            color: var(--text-color); 
            text-align: center; 
            padding: 12px; 
            background: white; 
            border-radius: 6px; 
            border: 2px dashed var(--primary-color); 
        }
        /* 渠道标签页样式 */
        .channel-tabs {
            display: flex;
            gap: 4px;
            margin-bottom: 24px;
            border-bottom: 1px solid var(--border-color);
        }

        .channel-tab {
            padding: 12px 24px;
            border: none;
            background: none;
            color: var(--text-color-secondary);
            font-weight: 500;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.2s;
        }

        .channel-tab.active {
            color: var(--primary-color);
            border-bottom-color: var(--primary-color);
        }

        .channel-tab:hover {
            color: var(--primary-color);
            background: var(--primary-color-light);
        }

        /* 场景卡片网格 */
        .scenarios-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            gap: 20px;
        }

        .scenario-card {
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 20px;
            background: var(--card-bg-color);
            transition: all 0.2s;
        }

        .scenario-card:hover {
            box-shadow: 0 8px 24px var(--shadow-color);
            transform: translateY(-2px);
        }

        .scenario-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .scenario-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-color);
            margin: 0;
        }

        .scenario-risk-level {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
        }

        .scenario-risk-level.high {
            background: rgba(255, 59, 48, 0.1);
            color: #ff3b30;
        }

        .scenario-risk-level.medium {
            background: rgba(255, 149, 0, 0.1);
            color: #ff9500;
        }

        .scenario-description {
            color: var(--text-color-secondary);
            font-size: 13px;
            margin-bottom: 16px;
            line-height: 1.4;
        }

        .policy-status {
            min-height: 80px;
            margin-bottom: 16px;
        }

        .policy-status.configured .policy-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .policy-name {
            font-weight: 600;
            color: var(--text-color);
        }

        .policy-version {
            background: var(--primary-color-light);
            color: var(--primary-color);
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 11px;
        }

        .policy-details {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .detail-item {
            background: var(--icon-hover-bg);
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            color: var(--text-color-secondary);
        }

        .policy-status.not-configured {
            display: flex;
            align-items: center;
            justify-content: center;
            border: 2px dashed var(--border-color);
            border-radius: 8px;
            color: var(--text-color-secondary);
        }

        .empty-state {
            text-align: center;
        }

        .empty-icon {
            width: 24px;
            height: 24px;
            margin-bottom: 8px;
        }

        .scenario-actions {
            display: flex;
            gap: 8px;
        }

        /* 策略配置向导样式 */
        .large-modal .modal-content {
            max-width: 900px;
            max-height: 80vh;
        }

        .wizard-progress {
            display: flex;
            justify-content: center;
            margin-bottom: 32px;
            padding: 0 20px;
        }

        .progress-step {
            display: flex;
            flex-direction: column;
            align-items: center;
            flex: 1;
            position: relative;
        }

        .progress-step:not(:last-child)::after {
            content: '';
            position: absolute;
            top: 15px;
            left: 60%;
            right: -40%;
            height: 2px;
            background: var(--border-color);
        }

        .progress-step.active:not(:last-child)::after {
            background: var(--primary-color);
        }

        .step-number {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: var(--border-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .progress-step.active .step-number {
            background: var(--primary-color);
        }

        .step-label {
            font-size: 12px;
            color: var(--text-color-secondary);
            text-align: center;
        }

        .progress-step.active .step-label {
            color: var(--primary-color);
            font-weight: 600;
        }

        .wizard-content {
            min-height: 400px;
            margin-bottom: 24px;
        }

        .wizard-step {
            display: none;
        }

        .wizard-step.active {
            display: block;
        }

        .step-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 24px;
            color: var(--text-color);
        }

        /* 渠道场景矩阵选择 */
        .channel-scenario-matrix {
            border: 1px solid var(--border-color);
            border-radius: 8px;
            overflow: hidden;
        }

        .matrix-row {
            display: flex;
            border-bottom: 1px solid var(--border-color);
        }

        .matrix-row:last-child {
            border-bottom: none;
        }

        .channel-label {
            width: 150px;
            padding: 16px;
            background: var(--primary-color-light);
            font-weight: 600;
            color: var(--primary-color);
            display: flex;
            align-items: center;
        }

        .scenario-options {
            flex: 1;
            display: flex;
            flex-wrap: wrap;
            padding: 8px;
            gap: 8px;
        }

        .scenario-option {
            flex: 1;
            min-width: 200px;
            cursor: pointer;
        }

        .scenario-option input {
            display: none;
        }

        .option-content {
            display: block;
            padding: 12px;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            transition: all 0.2s;
        }

        .scenario-option input:checked + .option-content {
            border-color: var(--primary-color);
            background: var(--primary-color-light);
        }

        .option-title {
            display: block;
            font-weight: 600;
            color: var(--text-color);
            margin-bottom: 4px;
        }

        .option-desc {
            display: block;
            font-size: 12px;
            color: var(--text-color-secondary);
        }

        /* 活体模式选择 */
        .liveness-modes {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
        }

        .mode-card {
            cursor: pointer;
        }

        .mode-card input {
            display: none;
        }

        .mode-content {
            border: 2px solid var(--border-color);
            border-radius: 12px;
            padding: 24px;
            text-align: center;
            transition: all 0.2s;
        }

        .mode-card input:checked + .mode-content {
            border-color: var(--primary-color);
            background: var(--primary-color-light);
        }

        .mode-icon {
            font-size: 48px;
            margin-bottom: 16px;
        }

        .mode-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--text-color);
        }

        .mode-desc {
            color: var(--text-color-secondary);
            margin-bottom: 16px;
        }

        .mode-features {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .feature {
            font-size: 12px;
            text-align: left;
        }

        .wizard-footer {
            display: flex;
            justify-content: space-between;
            padding-top: 24px;
            border-top: 1px solid var(--border-color);
        }
    </style>
</head>
<body data-theme="light">

    <aside class="sidebar">
        <div class="sidebar-header"><h1 class="sidebar-logo">SSID Platform</h1></div>
        <nav class="sidebar-nav">
            <ul class="nav-list">
                <li class="nav-item"><a href="dashboard.html"><svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"></path></svg><span>首页仪表盘</span></a></li>
                <li class="nav-item active"><a href="configuration.html"><svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path></svg><span>配置管理中心</span></a></li>
                <li class="nav-item"><a href="analytics.html"><svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path></svg><span>数据统计与分析</span></a></li>
                <li class="nav-item"><a href="risk-management.html"><svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path></svg><span>风险管控引擎</span></a></li>
                <li class="nav-item"><a href="case-audit.html"><svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path></svg><span>案例审计中心</span></a></li>
                <li class="nav-item"><a href="feature-sets.html"><svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4m0 5c0 2.21-3.582 4-8 4s-8-1.79-8-4"></path></svg><span>特征库管理</span></a></li>
                <li class="nav-item"><a href="distribution.html"><svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"></path></svg><span>资源分发中心</span></a></li>
                <li class="nav-item"><a href="settings.html"><svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 16v-2m8-8h2M4 12H2m15.364 6.364l-1.414-1.414M6.05 6.05L4.636 4.636m12.728 12.728L15.95 15.95M6.05 17.95l1.414-1.414M12 18a6 6 0 100-12 6 6 0 000 12z"></path></svg><span>系统管理</span></a></li>
            </ul>
        </nav>
    </aside>

    <div class="main-wrapper">
        <main class="main-content">
            <header class="page-header">
                <h1 class="page-title">配置管理中心</h1>
                <div class="header-actions">
                    <button class="btn btn-secondary" onclick="openChannelModal()">+ 管理渠道</button>
                    <button class="btn btn-secondary" onclick="openScenarioModal()">+ 管理场景</button>
                    <button class="theme-toggle" id="theme-toggle-btn" title="切换亮/暗模式">
                        <svg id="sun-icon" class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"></path></svg>
                        <svg id="moon-icon" class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path></svg>
                    </button>
                </div>
            </header>

            <!-- 策略配置矩阵视图 - 重新设计 -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">策略配置总览</h2>
                    <div class="header-actions">
                        <select class="form-select" style="margin-right: 12px;">
                            <option>全部策略</option>
                            <option>已配置</option>
                            <option>未配置</option>
                            <option>需要更新</option>
                        </select>
                        <button class="btn btn-primary" onclick="openPolicyWizard()">+ 配置新策略</button>
                    </div>
                </div>
                <div class="card-content">
                    <!-- 渠道标签页 -->
                    <div class="channel-tabs">
                        <button class="channel-tab active" onclick="switchChannel('mobile')">手机银行App</button>
                        <button class="channel-tab" onclick="switchChannel('web')">网银系统</button>
                        <button class="channel-tab" onclick="switchChannel('mini')">微信小程序</button>
                        <button class="channel-tab" onclick="switchChannel('all')">全部渠道</button>
                    </div>
                    
                    <!-- 场景策略卡片 -->
                    <div class="scenarios-grid" id="scenariosGrid">
                        <div class="scenario-card" data-scenario="register">
                            <div class="scenario-header">
                                <h3 class="scenario-title">用户注册</h3>
                                <span class="scenario-risk-level high">高风险</span>
                            </div>
                            <div class="scenario-description">新用户首次注册，需要严格身份验证</div>
                            <div class="policy-status configured">
                                <div class="policy-info">
                                    <span class="policy-name">严格静默活体策略</span>
                                    <span class="policy-version">v2.1</span>
                                </div>
                                <div class="policy-details">
                                    <span class="detail-item">静默活体</span>
                                    <span class="detail-item">阈值: 0.85</span>
                                    <span class="detail-item">质量检测: 开启</span>
                                </div>
                            </div>
                            <div class="scenario-actions">
                                <button class="btn btn-secondary btn-sm" onclick="editPolicy('mobile-register')">编辑策略</button>
                                <button class="btn btn-secondary btn-sm" onclick="viewPolicyHistory('mobile-register')">历史版本</button>
                            </div>
                        </div>

                        <div class="scenario-card" data-scenario="transfer">
                            <div class="scenario-header">
                                <h3 class="scenario-title">大额转账</h3>
                                <span class="scenario-risk-level high">高风险</span>
                            </div>
                            <div class="scenario-description">单笔转账金额超过5万元的交易验证</div>
                            <div class="policy-status configured">
                                <div class="policy-info">
                                    <span class="policy-name">交互活体增强策略</span>
                                    <span class="policy-version">v1.8</span>
                                </div>
                                <div class="policy-details">
                                    <span class="detail-item">交互活体</span>
                                    <span class="detail-item">动作: 2个</span>
                                    <span class="detail-item">阈值: 0.90</span>
                                </div>
                            </div>
                            <div class="scenario-actions">
                                <button class="btn btn-secondary btn-sm" onclick="editPolicy('mobile-transfer')">编辑策略</button>
                                <button class="btn btn-secondary btn-sm" onclick="viewPolicyHistory('mobile-transfer')">历史版本</button>
                            </div>
                        </div>

                        <div class="scenario-card" data-scenario="password">
                            <div class="scenario-header">
                                <h3 class="scenario-title">密码重置</h3>
                                <span class="scenario-risk-level medium">中风险</span>
                            </div>
                            <div class="scenario-description">用户忘记密码后的身份验证</div>
                            <div class="policy-status not-configured">
                                <div class="empty-state">
                                    <svg class="empty-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                    </svg>
                                    <span class="empty-text">未配置策略</span>
                                </div>
                            </div>
                            <div class="scenario-actions">
                                <button class="btn btn-primary btn-sm" onclick="createPolicy('mobile-password')">配置策略</button>
                                <button class="btn btn-secondary btn-sm" onclick="copyFromTemplate('password')">从模板复制</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 策略配置向导 -->
            <div id="policyWizard" class="modal large-modal">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3 class="modal-title">策略配置向导</h3>
                        <button class="modal-close" onclick="closePolicyWizard()">&times;</button>
                    </div>
                    <div class="wizard-progress">
                        <div class="progress-step active">
                            <div class="step-number">1</div>
                            <div class="step-label">选择应用场景</div>
                        </div>
                        <div class="progress-step">
                            <div class="step-number">2</div>
                            <div class="step-label">配置核验方式</div>
                        </div>
                        <div class="progress-step">
                            <div class="step-number">3</div>
                            <div class="step-label">设置参数阈值</div>
                        </div>
                        <div class="progress-step">
                            <div class="step-number">4</div>
                            <div class="step-label">确认并保存</div>
                        </div>
                    </div>
                    
                    <div class="wizard-content">
                        <!-- 步骤1: 选择应用场景 -->
                        <div class="wizard-step active" id="step1">
                            <h4 class="step-title">选择需要配置策略的渠道和场景</h4>
                            <div class="channel-scenario-matrix">
                                <div class="matrix-row">
                                    <div class="channel-label">手机银行App</div>
                                    <div class="scenario-options">
                                        <label class="scenario-option">
                                            <input type="checkbox" name="channelScenario" value="mobile-register">
                                            <span class="option-content">
                                                <span class="option-title">用户注册</span>
                                                <span class="option-desc">新用户首次注册验证</span>
                                            </span>
                                        </label>
                                        <label class="scenario-option">
                                            <input type="checkbox" name="channelScenario" value="mobile-transfer">
                                            <span class="option-content">
                                                <span class="option-title">大额转账</span>
                                                <span class="option-desc">单笔超过5万元转账</span>
                                            </span>
                                        </label>
                                        <label class="scenario-option">
                                            <input type="checkbox" name="channelScenario" value="mobile-password">
                                            <span class="option-content">
                                                <span class="option-title">密码重置</span>
                                                <span class="option-desc">忘记密码后重置</span>
                                            </span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 步骤2: 配置核验方式 -->
                        <div class="wizard-step" id="step2">
                            <h4 class="step-title">选择活体检测模式</h4>
                            <div class="liveness-modes">
                                <label class="mode-card">
                                    <input type="radio" name="livenessMode" value="silent">
                                    <div class="mode-content">
                                        <div class="mode-icon">🤫</div>
                                        <h5 class="mode-title">静默活体</h5>
                                        <p class="mode-desc">用户无需配合动作，体验最佳</p>
                                        <div class="mode-features">
                                            <span class="feature">✓ 用户体验好</span>
                                            <span class="feature">✓ 检测速度快</span>
                                            <span class="feature">⚠ 安全性中等</span>
                                        </div>
                                    </div>
                                </label>
                                
                                <label class="mode-card">
                                    <input type="radio" name="livenessMode" value="interactive">
                                    <div class="mode-content">
                                        <div class="mode-icon">👋</div>
                                        <h5 class="mode-title">交互活体</h5>
                                        <p class="mode-desc">用户需要完成指定动作</p>
                                        <div class="mode-features">
                                            <span class="feature">✓ 安全性高</span>
                                            <span class="feature">✓ 防攻击能力强</span>
                                            <span class="feature">⚠ 用户体验一般</span>
                                        </div>
                                    </div>
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="wizard-footer">
                        <button class="btn btn-secondary" onclick="previousStep()">上一步</button>
                        <button class="btn btn-primary" onclick="nextStep()">下一步</button>
                    </div>
                </div>
            </div>

            <script>
                // 更新渠道场景组合显示
                function updateCombination() {
                    const channelSelect = document.getElementById('channelSelect');
                    const scenarioSelect = document.getElementById('scenarioSelect');
                    const display = document.getElementById('combinationDisplay');
                    
                    const channelText = channelSelect.options[channelSelect.selectedIndex]?.text || '';
                    const scenarioText = scenarioSelect.options[scenarioSelect.selectedIndex]?.text || '';
                    
                    if (channelText && scenarioText) {
                        display.textContent = `${channelText} × ${scenarioText}`;
                        display.style.color = 'var(--primary-color)';
                        // 自动生成策略名称
                        document.querySelector('[name="policyName"]').value = `${channelText}_${scenarioText}_策略`;
                    } else {
                        display.textContent = '请先选择渠道和场景';
                        display.style.color = 'var(--text-color-secondary)';
                    }
                }

                // 创建特定渠道场景的策略
                function createPolicy(channel, scenario) {
                    openPolicyModal();
                    document.getElementById('channelSelect').value = channel;
                    document.getElementById('scenarioSelect').value = scenario;
                    updateCombination();
                }

                // 编辑现有策略
                function editPolicy(policyId) {
                    openPolicyModal();
                    document.querySelector('.modal-title').textContent = '编辑核验策略';
                    // 加载策略数据...
                }

                function openPolicyModal() {
                    document.getElementById('policyModal').classList.add('show');
                }

                function closePolicyModal() {
                    document.getElementById('policyModal').classList.remove('show');
                }

                // 主题切换功能
                const themeToggleBtn = document.getElementById('theme-toggle-btn');
                const sunIcon = document.getElementById('sun-icon');
                const moonIcon = document.getElementById('moon-icon');
                const body = document.body;

                const applyTheme = (theme) => {
                    body.setAttribute('data-theme', theme);
                    if (theme === 'dark') {
                        sunIcon.style.display = 'none';
                        moonIcon.style.display = 'block';
                    } else {
                        sunIcon.style.display = 'block';
                        moonIcon.style.display = 'none';
                    }
                };

                const savedTheme = localStorage.getItem('theme');
                const prefersDark = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
                const defaultTheme = savedTheme || (prefersDark ? 'dark' : 'light');
                applyTheme(defaultTheme);

                themeToggleBtn.addEventListener('click', () => {
                    const currentTheme = body.getAttribute('data-theme');
                    const newTheme = currentTheme === 'light' ? 'dark' : 'light';
                    localStorage.setItem('theme', newTheme);
                    applyTheme(newTheme);
                });
            </script>
        </main>
    </div>
</body>
</html>




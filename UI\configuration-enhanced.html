<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SSID Platform - 配置管理中心（增强版）</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        :root {
            --bg-color: #f6f8fb;
            --sidebar-bg-color: #ffffff;
            --card-bg-color: #ffffff;
            --text-color: #1e293b;
            --text-color-secondary: #64748b;
            --border-color: #e5eaf3;
            --primary-color: #2d78f4;
            --primary-color-light: #f0f6ff;
            --shadow-color: rgba(0, 0, 0, 0.06);
            --icon-color: #64748b;
            --icon-hover-bg: #f1f5f9;
            --success-color: #4cd964;
            --warning-color: #ff9500;
            --danger-color: #ff3b30;
        }

        [data-theme="dark"] {
            --bg-color: #111827;
            --sidebar-bg-color: #1f2937;
            --card-bg-color: #1f2937;
            --text-color: #f9fafb;
            --text-color-secondary: #9ca3af;
            --border-color: #374151;
            --primary-color: #3b82f6;
            --primary-color-light: #252e3d;
            --shadow-color: rgba(0, 0, 0, 0.2);
            --icon-color: #9ca3af;
            --icon-hover-bg: #374151;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: "PingFang SC", "Microsoft YaHei", sans-serif;
        }

        html,
        body {
            height: 100%;
            overflow: hidden;
        }

        body {
            background-color: var(--bg-color);
            color: var(--text-color);
            transition: background-color 0.3s, color 0.3s;
            display: flex;
        }

        .sidebar {
            width: 260px;
            background-color: var(--sidebar-bg-color);
            border-right: 1px solid var(--border-color);
            display: flex;
            flex-direction: column;
            height: 100%;
            flex-shrink: 0;
            transition: background-color 0.3s, border-color 0.3s;
        }

        .main-wrapper {
            flex-grow: 1;
            height: 100%;
            overflow-y: auto;
        }

        .main-content {
            padding: 24px;
        }

        .sidebar-header {
            display: flex;
            align-items: center;
            padding: 0 24px;
            height: 64px;
            border-bottom: 1px solid var(--border-color);
            flex-shrink: 0;
        }

        .sidebar-logo {
            font-size: 22px;
            font-weight: 700;
            color: var(--primary-color);
        }

        .sidebar-nav {
            flex-grow: 1;
            padding: 16px 0;
        }

        .nav-list {
            list-style: none;
        }

        .nav-item a {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 24px;
            margin: 4px 16px;
            border-radius: 8px;
            color: var(--text-color-secondary);
            text-decoration: none;
            font-weight: 500;
            transition: background-color 0.2s, color 0.2s;
        }

        .nav-item a:hover {
            background-color: var(--primary-color-light);
            color: var(--primary-color);
        }

        .nav-item.active a {
            background-color: var(--primary-color-light);
            color: var(--primary-color);
            font-weight: 600;
        }

        .nav-icon {
            width: 20px;
            height: 20px;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
        }

        .page-title {
            font-size: 24px;
            font-weight: 700;
        }

        .header-actions {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .theme-toggle {
            background-color: var(--icon-hover-bg);
            border: none;
            border-radius: 50%;
            width: 36px;
            height: 36px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--icon-color);
            transition: background-color 0.2s;
        }

        .theme-toggle .icon {
            width: 20px;
            height: 20px;
        }

        #moon-icon {
            display: none;
        }

        .card {
            background-color: var(--card-bg-color);
            border-radius: 12px;
            margin-bottom: 24px;
            box-shadow: 0 4px 12px var(--shadow-color);
            border: 1px solid var(--border-color);
            overflow: hidden;
            transition: background-color 0.3s, border-color 0.3s;
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px 24px;
            border-bottom: 1px solid var(--border-color);
        }

        .card-title {
            font-size: 17px;
            font-weight: 600;
            color: var(--text-color);
        }

        .card-content {
            padding: 24px;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th,
        .table td {
            text-align: left;
            padding: 12px 16px;
            border-bottom: 1px solid var(--border-color);
            font-size: 14px;
        }

        .table th {
            color: var(--text-color-secondary);
            font-weight: 500;
        }

        .btn {
            padding: 8px 16px;
            border-radius: 6px;
            border: none;
            font-size: 14px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.2s;
        }

        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .btn-secondary {
            background-color: var(--icon-hover-bg);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .btn-danger {
            background-color: rgba(255, 59, 48, 0.15);
            color: #ff3b30;
        }

        .btn-success {
            background-color: var(--success-color);
            color: white;
        }

        .action-buttons {
            display: flex;
            gap: 8px;
        }

        .badge {
            padding: 4px 10px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .badge-blue {
            background-color: rgba(45, 120, 244, 0.1);
            color: #2d78f4;
        }

        .badge-green {
            background-color: rgba(76, 217, 100, 0.1);
            color: #4cd964;
        }

        .badge-orange {
            background-color: rgba(255, 149, 0, 0.1);
            color: #ff9500;
        }

        /* 模态框样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }

        .modal.show {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background-color: var(--card-bg-color);
            border-radius: 12px;
            width: 90%;
            max-width: 800px;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 24px;
            border-bottom: 1px solid var(--border-color);
        }

        .modal-title {
            font-size: 18px;
            font-weight: 600;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: var(--text-color-secondary);
        }

        .modal-body {
            padding: 24px;
        }

        .modal-footer {
            display: flex;
            justify-content: flex-end;
            gap: 12px;
            padding: 20px 24px;
            border-top: 1px solid var(--border-color);
        }

        /* 表单样式 */
        .form-section {
            margin-bottom: 32px;
        }

        .form-section-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 16px;
            color: var(--text-color);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .form-section-title .icon {
            width: 20px;
            height: 20px;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .form-label {
            font-size: 14px;
            font-weight: 500;
            color: var(--text-color);
        }

        .form-label.required::after {
            content: " *";
            color: var(--danger-color);
        }

        .form-input,
        .form-select,
        .form-textarea {
            padding: 10px 12px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            background-color: var(--card-bg-color);
            color: var(--text-color);
            font-size: 14px;
            transition: border-color 0.2s;
        }

        .form-input:focus,
        .form-select:focus,
        .form-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(45, 120, 244, 0.1);
        }

        .form-textarea {
            resize: vertical;
            min-height: 80px;
        }

        .form-help {
            font-size: 12px;
            color: var(--text-color-secondary);
        }

        .form-error {
            font-size: 12px;
            color: var(--danger-color);
        }

        /* 活体模式选择器 */
        .liveness-mode-selector {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
        }

        .liveness-mode-card {
            border: 2px solid var(--border-color);
            border-radius: 8px;
            padding: 16px;
            cursor: pointer;
            transition: all 0.2s;
            text-align: center;
        }

        .liveness-mode-card:hover {
            border-color: var(--primary-color);
        }

        .liveness-mode-card.selected {
            border-color: var(--primary-color);
            background-color: var(--primary-color-light);
        }

        .liveness-mode-icon {
            width: 48px;
            height: 48px;
            margin: 0 auto 12px;
            background-color: var(--primary-color-light);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .liveness-mode-title {
            font-weight: 600;
            margin-bottom: 8px;
        }

        .liveness-mode-desc {
            font-size: 12px;
            color: var(--text-color-secondary);
        }

        /* 动态配置面板 */
        .dynamic-config-panel {
            margin-top: 20px;
            padding: 20px;
            background-color: var(--primary-color-light);
            border-radius: 8px;
            border: 1px solid var(--primary-color);
        }

        .dynamic-config-panel.hidden {
            display: none;
        }

        /* 阈值配置 */
        .threshold-config {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .threshold-item {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .threshold-label {
            min-width: 120px;
            font-weight: 500;
        }

        .threshold-slider {
            flex: 1;
        }

        .threshold-input {
            width: 80px;
        }

        .threshold-preview {
            font-size: 12px;
            color: var(--text-color-secondary);
            margin-top: 4px;
        }

        /* 质量控制开关 */
        .quality-controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
        }

        .quality-control-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
        }

        .toggle-switch {
            position: relative;
            width: 44px;
            height: 24px;
            background-color: var(--border-color);
            border-radius: 12px;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .toggle-switch.active {
            background-color: var(--primary-color);
        }

        .toggle-switch::after {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 20px;
            height: 20px;
            background-color: white;
            border-radius: 50%;
            transition: transform 0.2s;
        }

        .toggle-switch.active::after {
            transform: translateX(20px);
        }

        /* 版本控制 */
        .version-control {
            margin-top: 20px;
        }

        .version-history {
            max-height: 300px;
            overflow-y: auto;
        }

        .version-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px;
            border-bottom: 1px solid var(--border-color);
        }

        .version-info {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .version-number {
            font-weight: 600;
        }

        .version-date {
            font-size: 12px;
            color: var(--text-color-secondary);
        }

        .version-actions {
            display: flex;
            gap: 8px;
        }

        /* 多选器样式 */
        .multi-selector {
            border: 1px solid var(--border-color);
            border-radius: 6px;
            min-height: 100px;
            padding: 8px;
        }

        .multi-selector-item {
            display: inline-block;
            background-color: var(--primary-color-light);
            color: var(--primary-color);
            padding: 4px 8px;
            margin: 2px;
            border-radius: 4px;
            font-size: 12px;
        }

        .multi-selector-item .remove {
            margin-left: 4px;
            cursor: pointer;
        }

        /* 渠道场景树形结构 */
        .channel-scenario-tree {
            padding: 20px;
        }

        .tree-node {
            margin-bottom: 8px;
        }

        .tree-node-header,
        .tree-node-content {
            display: flex;
            align-items: center;
            padding: 12px;
            border-radius: 6px;
            transition: background-color 0.2s;
        }

        .tree-node-header:hover,
        .tree-node-content:hover {
            background-color: var(--icon-hover-bg);
        }

        .channel-node .tree-node-header {
            background-color: var(--primary-color-light);
            border: 1px solid var(--primary-color);
            font-weight: 600;
        }

        .scenario-node .tree-node-content {
            margin-left: 30px;
            background-color: var(--card-bg-color);
            border: 1px solid var(--border-color);
        }

        .tree-toggle {
            cursor: pointer;
            margin-right: 8px;
            font-size: 12px;
            width: 16px;
            text-align: center;
            user-select: none;
        }

        .tree-toggle.collapsed {
            transform: rotate(-90deg);
        }

        .tree-icon {
            margin-right: 8px;
            font-size: 16px;
        }

        .tree-label {
            flex-grow: 1;
            margin-right: 12px;
        }

        .tree-actions {
            display: flex;
            gap: 6px;
            margin-left: auto;
        }

        .tree-children {
            margin-left: 20px;
        }

        .btn-sm {
            padding: 4px 8px;
            font-size: 12px;
        }

        .scenario-info {
            font-size: 12px;
            color: var(--text-color-secondary);
            margin-right: 12px;
        }

        .empty-scenarios {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
            padding: 20px;
            color: var(--text-color-secondary);
            font-style: italic;
        }

        /* 渠道场景表单样式 */
        .channel-form,
        .scenario-form {
            max-width: 500px;
        }

        .channel-selector {
            margin-top: 12px;
        }

        .channel-option {
            display: flex;
            align-items: center;
            padding: 8px 12px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            margin-bottom: 8px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .channel-option:hover {
            border-color: var(--primary-color);
            background-color: var(--primary-color-light);
        }

        .channel-option.selected {
            border-color: var(--primary-color);
            background-color: var(--primary-color-light);
        }

        /* 渠道场景选择器 */
        .channel-scenario-picker {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin-top: 8px;
        }

        .channel-scenario-picker select:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        /* 阈值模式选择器 */
        .threshold-mode-selector {
            display: flex;
            gap: 20px;
            margin-top: 8px;
        }

        .threshold-mode-option {
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            font-size: 14px;
        }

        .threshold-mode-option input[type="radio"] {
            margin: 0;
        }

        /* 继承阈值信息显示 */
        .inherited-threshold-info {
            background-color: var(--primary-color-light);
            border: 1px solid var(--primary-color);
            border-radius: 6px;
            padding: 16px;
            margin-top: 12px;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 4px 0;
            font-size: 14px;
        }

        .info-label {
            color: var(--text-color-secondary);
        }

        .info-value {
            font-weight: 600;
            color: var(--text-color);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .form-grid {
                grid-template-columns: 1fr;
            }

            .liveness-mode-selector {
                grid-template-columns: 1fr;
            }

            .quality-controls {
                grid-template-columns: 1fr;
            }

            .modal-content {
                width: 95%;
                margin: 20px;
            }

            .tree-actions {
                flex-direction: column;
                gap: 4px;
            }

            .scenario-node .tree-node-content {
                margin-left: 15px;
            }
        }
    </style>
</head>

<body data-theme="light">

    <aside class="sidebar">
        <div class="sidebar-header">
            <h1 class="sidebar-logo">SSID Platform</h1>
        </div>
        <nav class="sidebar-nav">
            <ul class="nav-list">
                <li class="nav-item"><a href="dashboard.html"><svg class="nav-icon" fill="none" stroke="currentColor"
                            viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z">
                            </path>
                        </svg><span>首页仪表盘</span></a></li>
                <li class="nav-item active"><a href="configuration.html"><svg class="nav-icon" fill="none"
                            stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z">
                            </path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg><span>配置管理中心</span></a></li>
                <li class="nav-item"><a href="analytics.html"><svg class="nav-icon" fill="none" stroke="currentColor"
                            viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z">
                            </path>
                        </svg><span>数据统计与分析</span></a></li>
                <li class="nav-item"><a href="risk-management.html"><svg class="nav-icon" fill="none"
                            stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z">
                            </path>
                        </svg><span>风险管控引擎</span></a></li>
                <li class="nav-item"><a href="case-audit.html"><svg class="nav-icon" fill="none" stroke="currentColor"
                            viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                            </path>
                        </svg><span>案例审计中心</span></a></li>
                <li class="nav-item"><a href="feature-sets.html"><svg class="nav-icon" fill="none" stroke="currentColor"
                            viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4m0 5c0 2.21-3.582 4-8 4s-8-1.79-8-4">
                            </path>
                        </svg><span>特征库管理</span></a></li>
                <li class="nav-item"><a href="distribution.html"><svg class="nav-icon" fill="none" stroke="currentColor"
                            viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"></path>
                        </svg><span>资源分发中心</span></a></li>
                <li class="nav-item"><a href="settings.html"><svg class="nav-icon" fill="none" stroke="currentColor"
                            viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 6V4m0 16v-2m8-8h2M4 12H2m15.364 6.364l-1.414-1.414M6.05 6.05L4.636 4.636m12.728 12.728L15.95 15.95M6.05 17.95l1.414-1.414M12 18a6 6 0 100-12 6 6 0 000 12z">
                            </path>
                        </svg><span>系统管理</span></a></li>
            </ul>
        </nav>
    </aside>

    <div class="main-wrapper">
        <main class="main-content">
            <header class="page-header">
                <h1 class="page-title">配置管理中心</h1>
                <div class="header-actions">
                    <button class="btn btn-primary" onclick="openPolicyModal()">+ 新建核验策略</button>
                    <button class="theme-toggle" id="theme-toggle-btn" title="切换亮/暗模式">
                        <svg id="sun-icon" class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z">
                            </path>
                        </svg>
                        <svg id="moon-icon" class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z">
                            </path>
                        </svg>
                    </button>
                </div>
            </header>

            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">渠道与场景管理</h2>
                    <div class="header-actions">
                        <button class="btn btn-secondary" onclick="openChannelModal()">+ 添加渠道</button>
                        <button class="btn btn-secondary" onclick="openScenarioModal()">+ 添加场景</button>
                    </div>
                </div>
                <div class="card-content" style="padding: 0;">
                    <div class="channel-scenario-tree">
                        <!-- 手机银行 App 渠道 -->
                        <div class="tree-node channel-node">
                            <div class="tree-node-header">
                                <span class="tree-toggle" onclick="toggleTreeNode(this)">▼</span>
                                <span class="tree-label">手机银行 App</span>
                                <span class="badge badge-blue">渠道</span>
                                <div class="tree-actions">
                                    <button class="btn btn-secondary btn-sm"
                                        onclick="editChannel('mobile-app')">编辑</button>
                                    <button class="btn btn-secondary btn-sm"
                                        onclick="addScenarioToChannel('mobile-app')">添加场景</button>
                                    <button class="btn btn-danger btn-sm">删除</button>
                                </div>
                            </div>
                            <div class="tree-children">
                                <div class="tree-node scenario-node">
                                    <div class="tree-node-content">
                                        <span class="tree-label">用户注册</span>
                                        <span class="badge badge-green">场景</span>
                                        <span class="scenario-info">策略数: 2</span>
                                        <div class="tree-actions">
                                            <button class="btn btn-secondary btn-sm"
                                                onclick="editScenario('user-register')">编辑</button>
                                            <button class="btn btn-danger btn-sm">删除</button>
                                        </div>
                                    </div>
                                </div>
                                <div class="tree-node scenario-node">
                                    <div class="tree-node-content">
                                        <span class="tree-label">大额转账</span>
                                        <span class="badge badge-green">场景</span>
                                        <span class="scenario-info">策略数: 1</span>
                                        <div class="tree-actions">
                                            <button class="btn btn-secondary btn-sm"
                                                onclick="editScenario('large-transfer')">编辑</button>
                                            <button class="btn btn-danger btn-sm">删除</button>
                                        </div>
                                    </div>
                                </div>
                                <div class="tree-node scenario-node">
                                    <div class="tree-node-content">
                                        <span class="tree-label">密码重置</span>
                                        <span class="badge badge-green">场景</span>
                                        <span class="scenario-info">策略数: 1</span>
                                        <div class="tree-actions">
                                            <button class="btn btn-secondary btn-sm"
                                                onclick="editScenario('password-reset')">编辑</button>
                                            <button class="btn btn-danger btn-sm">删除</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 小程序渠道 -->
                        <div class="tree-node channel-node">
                            <div class="tree-node-header">
                                <span class="tree-toggle" onclick="toggleTreeNode(this)">▼</span>
                                <span class="tree-label">小程序</span>
                                <span class="badge badge-blue">渠道</span>
                                <div class="tree-actions">
                                    <button class="btn btn-secondary btn-sm"
                                        onclick="editChannel('mini-program')">编辑</button>
                                    <button class="btn btn-secondary btn-sm"
                                        onclick="addScenarioToChannel('mini-program')">添加场景</button>
                                    <button class="btn btn-danger btn-sm">删除</button>
                                </div>
                            </div>
                            <div class="tree-children">
                                <div class="tree-node scenario-node">
                                    <div class="tree-node-content">
                                        <span class="tree-label">用户注册</span>
                                        <span class="badge badge-green">场景</span>
                                        <span class="scenario-info">策略数: 1</span>
                                        <div class="tree-actions">
                                            <button class="btn btn-secondary btn-sm"
                                                onclick="editScenario('user-register-mini')">编辑</button>
                                            <button class="btn btn-danger btn-sm">删除</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 网页端渠道 -->
                        <div class="tree-node channel-node">
                            <div class="tree-node-header">
                                <span class="tree-toggle collapsed" onclick="toggleTreeNode(this)">▶</span>
                                <span class="tree-label">网页端</span>
                                <span class="badge badge-blue">渠道</span>
                                <div class="tree-actions">
                                    <button class="btn btn-secondary btn-sm" onclick="editChannel('web')">编辑</button>
                                    <button class="btn btn-secondary btn-sm"
                                        onclick="addScenarioToChannel('web')">添加场景</button>
                                    <button class="btn btn-danger btn-sm">删除</button>
                                </div>
                            </div>
                            <div class="tree-children" style="display: none;">
                                <div class="empty-scenarios">
                                    <span class="empty-text">暂无场景</span>
                                    <button class="btn btn-primary btn-sm"
                                        onclick="addScenarioToChannel('web')">添加第一个场景</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">核验策略列表</h2>
                    <div class="header-actions">
                        <select class="form-select" style="margin-right: 12px;">
                            <option>全部状态</option>
                            <option>已启用</option>
                            <option>已禁用</option>
                            <option>草稿</option>
                        </select>
                        <input type="search" placeholder="搜索策略..." class="form-input" style="width: 200px;">
                    </div>
                </div>
                <div class="card-content" style="padding: 0;">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>策略名称</th>
                                <th>关联渠道/场景</th>
                                <th>活体模式</th>
                                <th>版本</th>
                                <th>状态</th>
                                <th>更新时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>默认静默活体策略</td>
                                <td>手机银行 App / 用户注册</td>
                                <td><span class="badge badge-blue">静默活体</span></td>
                                <td>v1.2</td>
                                <td><span class="badge badge-green">已启用</span></td>
                                <td>2023-08-01</td>
                                <td class="action-buttons">
                                    <button class="btn btn-secondary" onclick="editPolicy('policy1')">编辑</button>
                                    <button class="btn btn-secondary"
                                        onclick="showVersionHistory('policy1')">版本历史</button>
                                    <button class="btn btn-secondary" onclick="duplicatePolicy('policy1')">复制</button>
                                    <button class="btn btn-danger">删除</button>
                                </td>
                            </tr>
                            <tr>
                                <td>高安全交互策略</td>
                                <td>手机银行 App / 大额转账</td>
                                <td><span class="badge badge-orange">交互活体</span></td>
                                <td>v2.1</td>
                                <td><span class="badge badge-green">已启用</span></td>
                                <td>2023-07-15</td>
                                <td class="action-buttons">
                                    <button class="btn btn-secondary" onclick="editPolicy('policy2')">编辑</button>
                                    <button class="btn btn-secondary"
                                        onclick="showVersionHistory('policy2')">版本历史</button>
                                    <button class="btn btn-secondary" onclick="duplicatePolicy('policy2')">复制</button>
                                    <button class="btn btn-danger">删除</button>
                                </td>
                            </tr>
                            <tr>
                                <td>小程序注册策略</td>
                                <td>小程序 / 用户注册</td>
                                <td><span class="badge badge-green">炫彩活体</span></td>
                                <td>v1.0</td>
                                <td><span class="badge badge-green">已启用</span></td>
                                <td>2023-08-05</td>
                                <td class="action-buttons">
                                    <button class="btn btn-secondary" onclick="editPolicy('policy3')">编辑</button>
                                    <button class="btn btn-secondary"
                                        onclick="showVersionHistory('policy3')">版本历史</button>
                                    <button class="btn btn-secondary" onclick="duplicatePolicy('policy3')">复制</button>
                                    <button class="btn btn-danger">删除</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </main>
    </div>

    <!-- 策略配置模态框 -->
    <div id="policyModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">新建核验策略</h3>
                <button class="modal-close" onclick="closePolicyModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="policyForm">
                    <!-- 基本信息 -->
                    <div class="form-section">
                        <div class="form-section-title">
                            <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            基本信息
                        </div>
                        <div class="form-grid">
                            <div class="form-group">
                                <label class="form-label required">策略名称</label>
                                <input type="text" class="form-input" name="policyName" placeholder="请输入策略名称" required>
                                <div class="form-help">策略名称应简洁明了，便于识别</div>
                            </div>
                            <div class="form-group">
                                <label class="form-label">策略描述</label>
                                <textarea class="form-textarea" name="policyDescription"
                                    placeholder="请输入策略描述"></textarea>
                            </div>
                        </div>
                        <div class="form-grid">
                            <div class="form-group">
                                <label class="form-label required">关联渠道/场景组合</label>
                                <div class="multi-selector" id="channelScenarioSelector">
                                    <div class="multi-selector-item">手机银行 App / 用户注册 <span class="remove">&times;</span>
                                    </div>
                                </div>
                                <div class="channel-scenario-picker">
                                    <select class="form-select" id="channelPicker"
                                        onchange="updateScenarioPicker(this)">
                                        <option value="">选择渠道...</option>
                                        <option value="mobile-app">手机银行 App</option>
                                        <option value="mini-program">小程序</option>
                                        <option value="web">网页端</option>
                                    </select>
                                    <select class="form-select" id="scenarioPicker" onchange="addChannelScenario()"
                                        disabled>
                                        <option value="">先选择渠道...</option>
                                    </select>
                                </div>
                                <div class="form-help">请先选择渠道，然后选择该渠道下的场景</div>
                            </div>
                        </div>
                    </div>

                    <!-- 活体模式配置 -->
                    <div class="form-section">
                        <div class="form-section-title">
                            <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                            活体模式配置
                        </div>
                        <div class="liveness-mode-selector">
                            <div class="liveness-mode-card selected" data-mode="silent">
                                <div class="liveness-mode-icon">
                                    <svg width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                                        <path
                                            d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" />
                                    </svg>
                                </div>
                                <div class="liveness-mode-title">静默活体</div>
                                <div class="liveness-mode-desc">无需用户配合，自动检测活体</div>
                            </div>
                            <div class="liveness-mode-card" data-mode="interactive">
                                <div class="liveness-mode-icon">
                                    <svg width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                                        <path
                                            d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
                                    </svg>
                                </div>
                                <div class="liveness-mode-title">交互活体</div>
                                <div class="liveness-mode-desc">需要用户配合完成指定动作</div>
                            </div>
                            <div class="liveness-mode-card" data-mode="colorful">
                                <div class="liveness-mode-icon">
                                    <svg width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                                        <path
                                            d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8 0-1.82.62-3.49 1.64-4.83 1.02-1.34 2.49-2.38 4.18-2.95.56-.19 1.15-.22 1.73-.22.58 0 1.17.03 1.73.22 1.69.57 3.16 1.61 4.18 2.95C18.38 8.51 20 10.18 20 12c0 4.41-3.59 8-8 8z" />
                                    </svg>
                                </div>
                                <div class="liveness-mode-title">炫彩活体</div>
                                <div class="liveness-mode-desc">通过屏幕变色检测活体</div>
                            </div>
                        </div>

                        <!-- 交互活体动态配置 -->
                        <div id="interactiveConfig" class="dynamic-config-panel hidden">
                            <div class="form-section-title">交互活体配置</div>
                            <div class="form-grid">
                                <div class="form-group">
                                    <label class="form-label">动作序列</label>
                                    <div class="multi-selector" id="actionSelector">
                                        <div class="multi-selector-item">眨眼 <span class="remove">&times;</span></div>
                                        <div class="multi-selector-item">张嘴 <span class="remove">&times;</span></div>
                                    </div>
                                    <select class="form-select" onchange="addAction(this)">
                                        <option value="">选择动作...</option>
                                        <option value="blink">眨眼</option>
                                        <option value="open-mouth">张嘴</option>
                                        <option value="turn-left">向左转头</option>
                                        <option value="turn-right">向右转头</option>
                                        <option value="nod">点头</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">动作数量</label>
                                    <input type="number" class="form-input" name="actionCount" value="2" min="1"
                                        max="5">
                                    <div class="form-help">建议1-3个动作，过多会影响用户体验</div>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">超时时间（秒）</label>
                                    <input type="number" class="form-input" name="timeout" value="30" min="10" max="60">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 阈值参数配置 -->
                    <div class="form-section">
                        <div class="form-section-title">
                            <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z">
                                </path>
                            </svg>
                            阈值参数配置
                        </div>
                        <div class="threshold-config">
                            <div class="threshold-item">
                                <div class="threshold-label">活体分数阈值</div>
                                <input type="range" class="threshold-slider" min="0" max="1" step="0.01" value="0.8"
                                    oninput="updateThreshold(this, 'liveness')">
                                <input type="number" class="threshold-input form-input" min="0" max="1" step="0.01"
                                    value="0.8" onchange="updateSlider(this, 'liveness')">
                                <div class="threshold-preview">当前设置：通过率约85%，误拒率约3%</div>
                            </div>
                            <div class="threshold-item">
                                <div class="threshold-label">1:1比对阈值</div>
                                <input type="range" class="threshold-slider" min="0" max="1" step="0.01" value="0.75"
                                    oninput="updateThreshold(this, 'comparison')">
                                <input type="number" class="threshold-input form-input" min="0" max="1" step="0.01"
                                    value="0.75" onchange="updateSlider(this, 'comparison')">
                                <div class="threshold-preview">当前设置：通过率约90%，误拒率约2%</div>
                            </div>
                            <div class="threshold-item">
                                <div class="threshold-label">Deepfake阈值</div>
                                <input type="range" class="threshold-slider" min="0" max="1" step="0.01" value="0.9"
                                    oninput="updateThreshold(this, 'deepfake')">
                                <input type="number" class="threshold-input form-input" min="0" max="1" step="0.01"
                                    value="0.9" onchange="updateSlider(this, 'deepfake')">
                                <div class="threshold-preview">当前设置：检出率约95%，误报率约1%</div>
                            </div>
                        </div>
                    </div>

                    <!-- 人脸质量控制 -->
                    <div class="form-section">
                        <div class="form-section-title">
                            <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            人脸质量控制
                        </div>
                        <div class="quality-controls">
                            <div class="quality-control-item">
                                <span>允许闭眼</span>
                                <div class="toggle-switch" onclick="toggleSwitch(this)"></div>
                            </div>
                            <div class="quality-control-item">
                                <span>允许张嘴</span>
                                <div class="toggle-switch active" onclick="toggleSwitch(this)"></div>
                            </div>
                            <div class="quality-control-item">
                                <span>允许面部遮挡</span>
                                <div class="toggle-switch" onclick="toggleSwitch(this)"></div>
                            </div>
                            <div class="quality-control-item">
                                <span>允许侧脸</span>
                                <div class="toggle-switch" onclick="toggleSwitch(this)"></div>
                            </div>
                        </div>
                    </div>

                    <!-- 数据返回配置 -->
                    <div class="form-section">
                        <div class="form-section-title">
                            <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"></path>
                            </svg>
                            数据返回配置
                        </div>
                        <div class="quality-controls">
                            <div class="quality-control-item">
                                <span>返回最佳人脸图</span>
                                <div class="toggle-switch active" onclick="toggleSwitch(this)"></div>
                            </div>
                            <div class="quality-control-item">
                                <span>返回全过程视频</span>
                                <div class="toggle-switch" onclick="toggleSwitch(this)"></div>
                            </div>
                            <div class="quality-control-item">
                                <span>返回处理日志</span>
                                <div class="toggle-switch" onclick="toggleSwitch(this)"></div>
                            </div>
                            <div class="quality-control-item">
                                <span>返回算法分数</span>
                                <div class="toggle-switch active" onclick="toggleSwitch(this)"></div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closePolicyModal()">取消</button>
                <button class="btn btn-secondary" onclick="saveDraft()">保存草稿</button>
                <button class="btn btn-success" onclick="savePolicy()">保存并启用</button>
            </div>
        </div>
    </div>

    <!-- 渠道管理模态框 -->
    <div id="channelModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">添加渠道</h3>
                <button class="modal-close" onclick="closeChannelModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="channelForm" class="channel-form">
                    <div class="form-group">
                        <label class="form-label required">渠道名称</label>
                        <input type="text" class="form-input" name="channelName" placeholder="请输入渠道名称" required>
                        <div class="form-help">例如：手机银行App、小程序、网页端等</div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">渠道描述</label>
                        <textarea class="form-textarea" name="channelDescription" placeholder="请输入渠道描述"></textarea>
                    </div>
            </div>
            <div class="form-group">
                <label class="form-label">渠道标识</label>
                <input type="text" class="form-input" name="channelCode" placeholder="请输入渠道标识（英文）">
                <div class="form-help">用于API调用的唯一标识，建议使用英文和下划线</div>
            </div>

            <!-- 默认阈值配置 -->
            <div class="form-section">
                <div class="form-section-title">默认阈值配置</div>
                <div class="form-help" style="margin-bottom: 16px;">为该渠道设置默认的核验阈值，场景可以选择继承或自定义</div>

                <div class="threshold-config">
                    <div class="threshold-item">
                        <div class="threshold-label">活体分数阈值</div>
                        <input type="range" class="threshold-slider" min="0" max="1" step="0.01" value="0.8"
                            oninput="updateChannelThreshold(this, 'liveness')">
                        <input type="number" class="threshold-input form-input" min="0" max="1" step="0.01" value="0.8"
                            onchange="updateChannelSlider(this, 'liveness')">
                        <div class="threshold-preview">当前设置：通过率约85%，误拒率约3%</div>
                    </div>
                    <div class="threshold-item">
                        <div class="threshold-label">1:1比对阈值</div>
                        <input type="range" class="threshold-slider" min="0" max="1" step="0.01" value="0.75"
                            oninput="updateChannelThreshold(this, 'comparison')">
                        <input type="number" class="threshold-input form-input" min="0" max="1" step="0.01" value="0.75"
                            onchange="updateChannelSlider(this, 'comparison')">
                        <div class="threshold-preview">当前设置：通过率约90%，误拒率约2%</div>
                    </div>
                    <div class="threshold-item">
                        <div class="threshold-label">Deepfake阈值</div>
                        <input type="range" class="threshold-slider" min="0" max="1" step="0.01" value="0.9"
                            oninput="updateChannelThreshold(this, 'deepfake')">
                        <input type="number" class="threshold-input form-input" min="0" max="1" step="0.01" value="0.9"
                            onchange="updateChannelSlider(this, 'deepfake')">
                        <div class="threshold-preview">当前设置：检出率约95%，误报率约1%</div>
                    </div>
                </div>
            </div>
            </form>
        </div>
        <div class="modal-footer">
            <button class="btn btn-secondary" onclick="closeChannelModal()">取消</button>
            <button class="btn btn-primary" onclick="saveChannel()">保存</button>
        </div>
    </div>
    </div>

    <!-- 场景管理模态框 -->
    <div id="scenarioModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">添加场景</h3>
                <button class="modal-close" onclick="closeScenarioModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="scenarioForm" class="scenario-form">
                    <div class="form-group">
                        <label class="form-label required">场景名称</label>
                        <input type="text" class="form-input" name="scenarioName" placeholder="请输入场景名称" required>
                        <div class="form-help">例如：用户注册、大额转账、密码重置等</div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">场景描述</label>
                        <textarea class="form-textarea" name="scenarioDescription" placeholder="请输入场景描述"></textarea>
                    </div>
                    <div class="form-group">
                        <label class="form-label required">所属渠道</label>
                        <div class="channel-selector" id="channelSelector">
                            <div class="channel-option selected" data-channel="mobile-app">
                                <span>手机银行 App</span>
                            </div>
                            <div class="channel-option" data-channel="mini-program">
                                <span>小程序</span>
                            </div>
                            <div class="channel-option" data-channel="web">
                                <span>网页端</span>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">场景标识</label>
                        <input type="text" class="form-input" name="scenarioCode" placeholder="请输入场景标识（英文）">
                        <div class="form-help">用于API调用的唯一标识，建议使用英文和下划线</div>
                    </div>

                    <!-- 阈值配置 -->
                    <div class="form-section">
                        <div class="form-section-title">阈值配置</div>

                        <div class="form-group">
                            <label class="form-label">阈值设置方式</label>
                            <div class="threshold-mode-selector">
                                <label class="threshold-mode-option">
                                    <input type="radio" name="thresholdMode" value="inherit" checked
                                        onchange="toggleThresholdMode(this)">
                                    <span>继承渠道默认阈值</span>
                                </label>
                                <label class="threshold-mode-option">
                                    <input type="radio" name="thresholdMode" value="custom"
                                        onchange="toggleThresholdMode(this)">
                                    <span>自定义阈值</span>
                                </label>
                            </div>
                            <div class="form-help">选择继承渠道默认阈值或为该场景单独设置阈值</div>
                        </div>

                        <div id="customThresholdConfig" class="threshold-config" style="display: none;">
                            <div class="threshold-item">
                                <div class="threshold-label">活体分数阈值</div>
                                <input type="range" class="threshold-slider" min="0" max="1" step="0.01" value="0.8"
                                    oninput="updateScenarioThreshold(this, 'liveness')">
                                <input type="number" class="threshold-input form-input" min="0" max="1" step="0.01"
                                    value="0.8" onchange="updateScenarioSlider(this, 'liveness')">
                                <div class="threshold-preview">当前设置：通过率约85%，误拒率约3%</div>
                            </div>
                            <div class="threshold-item">
                                <div class="threshold-label">1:1比对阈值</div>
                                <input type="range" class="threshold-slider" min="0" max="1" step="0.01" value="0.75"
                                    oninput="updateScenarioThreshold(this, 'comparison')">
                                <input type="number" class="threshold-input form-input" min="0" max="1" step="0.01"
                                    value="0.75" onchange="updateScenarioSlider(this, 'comparison')">
                                <div class="threshold-preview">当前设置：通过率约90%，误拒率约2%</div>
                            </div>
                            <div class="threshold-item">
                                <div class="threshold-label">Deepfake阈值</div>
                                <input type="range" class="threshold-slider" min="0" max="1" step="0.01" value="0.9"
                                    oninput="updateScenarioThreshold(this, 'deepfake')">
                                <input type="number" class="threshold-input form-input" min="0" max="1" step="0.01"
                                    value="0.9" onchange="updateScenarioSlider(this, 'deepfake')">
                                <div class="threshold-preview">当前设置：检出率约95%，误报率约1%</div>
                            </div>
                        </div>

                        <div id="inheritedThresholdInfo" class="inherited-threshold-info">
                            <div class="info-item">
                                <span class="info-label">活体分数阈值:</span>
                                <span class="info-value" id="inheritedLiveness">0.80</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">1:1比对阈值:</span>
                                <span class="info-value" id="inheritedComparison">0.75</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Deepfake阈值:</span>
                                <span class="info-value" id="inheritedDeepfake">0.90</span>
                            </div>
                        </div>
                    </div>""

                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeScenarioModal()">取消</button>
                <button class="btn btn-primary" onclick="saveScenario()">保存</button>
            </div>
        </div>
    </div>

    <!-- 版本历史模态框 -->
    <div id="versionModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">版本历史</h3>
                <button class="modal-close" onclick="closeVersionModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="version-control">
                    <div class="version-history">
                        <div class="version-item">
                            <div class="version-info">
                                <div class="version-number">v1.2 (当前版本)</div>
                                <div class="version-date">2023-08-01 14:30:25</div>
                                <div style="font-size: 12px; color: var(--text-color-secondary);">调整活体阈值，优化通过率</div>
                            </div>
                            <div class="version-actions">
                                <button class="btn btn-secondary" disabled>当前版本</button>
                            </div>
                        </div>
                        <div class="version-item">
                            <div class="version-info">
                                <div class="version-number">v1.1</div>
                                <div class="version-date">2023-07-20 09:15:10</div>
                                <div style="font-size: 12px; color: var(--text-color-secondary);">增加Deepfake检测配置</div>
                            </div>
                            <div class="version-actions">
                                <button class="btn btn-secondary" onclick="compareVersion('v1.1')">对比</button>
                                <button class="btn btn-primary" onclick="rollbackVersion('v1.1')">回滚</button>
                            </div>
                        </div>
                        <div class="version-item">
                            <div class="version-info">
                                <div class="version-number">v1.0</div>
                                <div class="version-date">2023-07-01 16:45:30</div>
                                <div style="font-size: 12px; color: var(--text-color-secondary);">初始版本</div>
                            </div>
                            <div class="version-actions">
                                <button class="btn btn-secondary" onclick="compareVersion('v1.0')">对比</button>
                                <button class="btn btn-primary" onclick="rollbackVersion('v1.0')">回滚</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeVersionModal()">关闭</button>
            </div>
        </div>
    </div>

    <script src="js/ChannelScenarioManager.js"></script>
    <script>
        // 主题切换
        const themeToggleBtn = document.getElementById('theme-toggle-btn');
        const sunIcon = document.getElementById('sun-icon');
        const moonIcon = document.getElementById('moon-icon');
        const body = document.body;

        const applyTheme = (theme) => {
            body.setAttribute('data-theme', theme);
            if (theme === 'dark') {
                sunIcon.style.display = 'none';
                moonIcon.style.display = 'block';
            } else {
                sunIcon.style.display = 'block';
                moonIcon.style.display = 'none';
            }
        };

        const savedTheme = localStorage.getItem('theme');
        const prefersDark = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
        const defaultTheme = savedTheme || (prefersDark ? 'dark' : 'light');
        applyTheme(defaultTheme);

        themeToggleBtn.addEventListener('click', () => {
            const currentTheme = body.getAttribute('data-theme');
            const newTheme = currentTheme === 'light' ? 'dark' : 'light';
            localStorage.setItem('theme', newTheme);
            applyTheme(newTheme);
        });

        // 模态框控制
        function openPolicyModal() {
            document.getElementById('policyModal').classList.add('show');
        }

        function closePolicyModal() {
            document.getElementById('policyModal').classList.remove('show');
        }

        // 渠道管理
        function openChannelModal() {
            document.getElementById('channelModal').classList.add('show');
            document.querySelector('#channelModal .modal-title').textContent = '添加渠道';
            const form = document.getElementById('channelForm');
            form.reset();
            delete form.dataset.editId;
        }

        function closeChannelModal() {
            document.getElementById('channelModal').classList.remove('show');
        }

        function editChannel(channelId) {
            openChannelModal();
            document.querySelector('#channelModal .modal-title').textContent = '编辑渠道';
            // 加载渠道数据到表单
            loadChannelData(channelId);
        }

        async function saveChannel() {
            try {
                const form = document.getElementById('channelForm');
                const formData = new FormData(form);
                // 收集阈值配置
                const thresholds = {
                    liveness: parseFloat(form.querySelector('[oninput*="liveness"]').value),
                    comparison: parseFloat(form.querySelector('[oninput*="comparison"]').value),
                    deepfake: parseFloat(form.querySelector('[oninput*="deepfake"]').value)
                };

                const channelData = {
                    name: formData.get('channelName'),
                    description: formData.get('channelDescription'),
                    code: formData.get('channelCode'),
                    defaultThresholds: thresholds
                };

                // 使用管理器保存渠道
                const isEdit = form.dataset.editId;
                if (isEdit) {
                    await window.channelScenarioManager.updateChannel(isEdit, channelData);
                    alert('渠道更新成功');
                } else {
                    await window.channelScenarioManager.createChannel(channelData);
                    alert('渠道创建成功');
                }

                closeChannelModal();
            } catch (error) {
                alert('保存失败: ' + error.message);
            }
        }

        // 场景管理
        function openScenarioModal() {
            document.getElementById('scenarioModal').classList.add('show');
            document.querySelector('#scenarioModal .modal-title').textContent = '添加场景';
            const form = document.getElementById('scenarioForm');
            form.reset();
            delete form.dataset.editId;
            selectChannel(document.querySelector('#scenarioModal .channel-option.selected'));
        }

        function closeScenarioModal() {
            document.getElementById('scenarioModal').classList.remove('show');
        }

        function addScenarioToChannel(channelId) {
            openScenarioModal();
            // 预选择指定渠道
            document.querySelectorAll('#scenarioModal .channel-option').forEach(option => {
                option.classList.remove('selected');
                if (option.dataset.channel === channelId) {
                    option.classList.add('selected');
                }
            });
        }

        function editScenario(scenarioId) {
            openScenarioModal();
            document.querySelector('#scenarioModal .modal-title').textContent = '编辑场景';
            // 加载场景数据到表单
            loadScenarioData(scenarioId);
        }

        async function saveScenario() {
            try {
                const form = document.getElementById('scenarioForm');
                const formData = new FormData(form);
                const selectedChannel = document.querySelector('#scenarioModal .channel-option.selected');
                const thresholdMode = formData.get('thresholdMode');

                const scenarioData = {
                    name: formData.get('scenarioName'),
                    description: formData.get('scenarioDescription'),
                    code: formData.get('scenarioCode'),
                    channelId: selectedChannel ? selectedChannel.dataset.channel : null,
                    thresholdMode: thresholdMode
                };

                // 如果是自定义阈值，收集阈值配置
                if (thresholdMode === 'custom') {
                    const customConfig = document.getElementById('customThresholdConfig');
                    scenarioData.customThresholds = {
                        liveness: parseFloat(customConfig.querySelector('[oninput*="liveness"]').value),
                        comparison: parseFloat(customConfig.querySelector('[oninput*="comparison"]').value),
                        deepfake: parseFloat(customConfig.querySelector('[oninput*="deepfake"]').value)
                    };
                }

                // 使用管理器保存场景
                const isEdit = form.dataset.editId;
                if (isEdit) {
                    await window.channelScenarioManager.updateScenario(isEdit, scenarioData);
                    alert('场景更新成功');
                } else {
                    await window.channelScenarioManager.createScenario(scenarioData);
                    alert('场景创建成功');
                }

                closeScenarioModal();
            } catch (error) {
                alert('保存失败: ' + error.message);
            }
        }



        // 渠道选择
        function selectChannel(channelElement) {
            const container = channelElement.closest('.channel-selector');
            container.querySelectorAll('.channel-option').forEach(option => {
                option.classList.remove('selected');
            });
            channelElement.classList.add('selected');
        }

        // 树形结构控制
        function toggleTreeNode(toggleElement) {
            const treeNode = toggleElement.closest('.tree-node');
            const children = treeNode.querySelector('.tree-children');

            if (toggleElement.classList.contains('collapsed')) {
                toggleElement.classList.remove('collapsed');
                toggleElement.textContent = '▼';
                children.style.display = 'block';
            } else {
                toggleElement.classList.add('collapsed');
                toggleElement.textContent = '▶';
                children.style.display = 'none';
            }
        }

        function loadChannelData(channelId) {
            const channel = window.channelScenarioManager.getChannel(channelId);
            if (channel) {
                const form = document.getElementById('channelForm');
                form.dataset.editId = channelId;

                document.querySelector('[name="channelName"]').value = channel.name;
                document.querySelector('[name="channelDescription"]').value = channel.description || '';
                document.querySelector('[name="channelCode"]').value = channel.code;

                // 加载默认阈值配置
                if (channel.defaultThresholds) {
                    const livenessSlider = form.querySelector('[oninput*="liveness"]');
                    const comparisonSlider = form.querySelector('[oninput*="comparison"]');
                    const deepfakeSlider = form.querySelector('[oninput*="deepfake"]');

                    if (livenessSlider) {
                        livenessSlider.value = channel.defaultThresholds.liveness;
                        updateChannelThreshold(livenessSlider, 'liveness');
                    }
                    if (comparisonSlider) {
                        comparisonSlider.value = channel.defaultThresholds.comparison;
                        updateChannelThreshold(comparisonSlider, 'comparison');
                    }
                    if (deepfakeSlider) {
                        deepfakeSlider.value = channel.defaultThresholds.deepfake;
                        updateChannelThreshold(deepfakeSlider, 'deepfake');
                    }
                }
            }
        }

        function loadScenarioData(scenarioId) {
            const scenario = window.channelScenarioManager.getScenario(scenarioId);
            if (scenario) {
                const form = document.getElementById('scenarioForm');
                form.dataset.editId = scenarioId;

                document.querySelector('[name="scenarioName"]').value = scenario.name;
                document.querySelector('[name="scenarioDescription"]').value = scenario.description || '';
                document.querySelector('[name="scenarioCode"]').value = scenario.code;

                // 设置阈值模式
                const thresholdMode = scenario.thresholdMode || 'inherit';
                document.querySelector(`[name="thresholdMode"][value="${thresholdMode}"]`).checked = true;

                // 切换阈值模式显示
                toggleThresholdMode(document.querySelector(`[name="thresholdMode"][value="${thresholdMode}"]`));

                // 如果是自定义阈值，加载阈值配置
                if (thresholdMode === 'custom' && scenario.customThresholds) {
                    const customConfig = document.getElementById('customThresholdConfig');
                    const livenessSlider = customConfig.querySelector('[oninput*="liveness"]');
                    const comparisonSlider = customConfig.querySelector('[oninput*="comparison"]');
                    const deepfakeSlider = customConfig.querySelector('[oninput*="deepfake"]');

                    if (livenessSlider) {
                        livenessSlider.value = scenario.customThresholds.liveness;
                        updateScenarioThreshold(livenessSlider, 'liveness');
                    }
                    if (comparisonSlider) {
                        comparisonSlider.value = scenario.customThresholds.comparison;
                        updateScenarioThreshold(comparisonSlider, 'comparison');
                    }
                    if (deepfakeSlider) {
                        deepfakeSlider.value = scenario.customThresholds.deepfake;
                        updateScenarioThreshold(deepfakeSlider, 'deepfake');
                    }
                }



                // 选择渠道
                document.querySelectorAll('#scenarioModal .channel-option').forEach(option => {
                    option.classList.remove('selected');
                    if (option.dataset.channel === scenario.channelId) {
                        option.classList.add('selected');
                    }
                });
            }
        }

        async function deleteChannel(channelId) {
            try {
                const confirmed = confirm('确定要删除这个渠道吗？');
                if (confirmed) {
                    await window.channelScenarioManager.deleteChannel(channelId);
                    alert('渠道删除成功');
                }
            } catch (error) {
                alert('删除失败: ' + error.message);
            }
        }

        async function deleteScenario(scenarioId) {
            try {
                const confirmed = confirm('确定要删除这个场景吗？');
                if (confirmed) {
                    await window.channelScenarioManager.deleteScenario(scenarioId);
                    alert('场景删除成功');
                }
            } catch (error) {
                alert('删除失败: ' + error.message);
            }
        }

        function refreshChannelScenarioTree() {
            // 刷新渠道场景树
            window.channelScenarioManager.renderChannelScenarioTree();
        }

        // 更新场景选择器
        function updateScenarioPicker(channelSelect) {
            const channelId = channelSelect.value;
            window.channelScenarioManager.updateScenarioPickerForChannel(channelId);
        }

        // 渠道阈值配置函数
        function updateChannelThreshold(slider, type) {
            const input = slider.parentElement.querySelector('.threshold-input');
            input.value = slider.value;
            updateChannelThresholdPreview(slider.value, type, slider.parentElement.querySelector('.threshold-preview'));
        }

        function updateChannelSlider(input, type) {
            const slider = input.parentElement.querySelector('.threshold-slider');
            slider.value = input.value;
            updateChannelThresholdPreview(input.value, type, input.parentElement.querySelector('.threshold-preview'));
        }

        function updateChannelThresholdPreview(value, type, previewElement) {
            let text = '';
            const numValue = parseFloat(value);

            switch (type) {
                case 'liveness':
                    const passRate = Math.round((1 - numValue) * 20 + 80);
                    const rejectRate = Math.round(numValue * 5);
                    text = `当前设置：通过率约${passRate}%，误拒率约${rejectRate}%`;
                    break;
                case 'comparison':
                    const compPassRate = Math.round((1 - numValue) * 15 + 85);
                    const compRejectRate = Math.round(numValue * 3);
                    text = `当前设置：通过率约${compPassRate}%，误拒率约${compRejectRate}%`;
                    break;
                case 'deepfake':
                    const detectRate = Math.round(numValue * 100);
                    const falseRate = Math.round((1 - numValue) * 5);
                    text = `当前设置：检出率约${detectRate}%，误报率约${falseRate}%`;
                    break;
            }

            if (previewElement) {
                previewElement.textContent = text;
            }
        }

        // 场景阈值配置函数
        function updateScenarioThreshold(slider, type) {
            const input = slider.parentElement.querySelector('.threshold-input');
            input.value = slider.value;
            updateChannelThresholdPreview(slider.value, type, slider.parentElement.querySelector('.threshold-preview'));
        }

        function updateScenarioSlider(input, type) {
            const slider = input.parentElement.querySelector('.threshold-slider');
            slider.value = input.value;
            updateChannelThresholdPreview(input.value, type, input.parentElement.querySelector('.threshold-preview'));
        }

        // 切换阈值模式
        function toggleThresholdMode(radio) {
            const customConfig = document.getElementById('customThresholdConfig');
            const inheritedInfo = document.getElementById('inheritedThresholdInfo');

            if (radio.value === 'custom') {
                customConfig.style.display = 'block';
                inheritedInfo.style.display = 'none';
            } else {
                customConfig.style.display = 'none';
                inheritedInfo.style.display = 'block';
                // 更新继承的阈值信息
                updateInheritedThresholdInfo();
            }
        }

        // 更新继承的阈值信息
        function updateInheritedThresholdInfo() {
            const selectedChannel = document.querySelector('#scenarioModal .channel-option.selected');
            if (selectedChannel) {
                const channelId = selectedChannel.dataset.channel;
                const channel = window.channelScenarioManager.getChannel(channelId);
                if (channel && channel.defaultThresholds) {
                    document.getElementById('inheritedLiveness').textContent = channel.defaultThresholds.liveness.toFixed(2);
                    document.getElementById('inheritedComparison').textContent = channel.defaultThresholds.comparison.toFixed(2);
                    document.getElementById('inheritedDeepfake').textContent = channel.defaultThresholds.deepfake.toFixed(2);
                }
            }
        }

        function showVersionHistory(policyId) {
            document.getElementById('versionModal').classList.add('show');
        }

        function closeVersionModal() {
            document.getElementById('versionModal').classList.remove('show');
        }

        // 活体模式选择
        document.querySelectorAll('.liveness-mode-card').forEach(card => {
            card.addEventListener('click', function () {
                document.querySelectorAll('.liveness-mode-card').forEach(c => c.classList.remove('selected'));
                this.classList.add('selected');

                const mode = this.dataset.mode;
                const interactiveConfig = document.getElementById('interactiveConfig');

                if (mode === 'interactive') {
                    interactiveConfig.classList.remove('hidden');
                } else {
                    interactiveConfig.classList.add('hidden');
                }
            });
        });

        // 渠道场景选择器功能
        const channelScenarioData = {
            'mobile-app': {
                name: '手机银行 App',
                icon: '📱',
                scenarios: [
                    { value: 'user-register', name: '用户注册', icon: '🔐' },
                    { value: 'large-transfer', name: '大额转账', icon: '💰' },
                    { value: 'password-reset', name: '密码重置', icon: '🔑' }
                ]
            },
            'mini-program': {
                name: '小程序',
                icon: '📲',
                scenarios: [
                    { value: 'user-register-mini', name: '用户注册', icon: '🔐' }
                ]
            },
            'web': {
                name: '网页端',
                icon: '🌐',
                scenarios: []
            }
        };

        function updateScenarioPicker(channelSelect) {
            const scenarioPicker = document.getElementById('scenarioPicker');
            const channelValue = channelSelect.value;

            // 清空场景选择器
            scenarioPicker.innerHTML = '<option value="">选择场景...</option>';

            if (channelValue && channelScenarioData[channelValue]) {
                const scenarios = channelScenarioData[channelValue].scenarios;

                if (scenarios.length > 0) {
                    scenarios.forEach(scenario => {
                        const option = document.createElement('option');
                        option.value = scenario.value;
                        option.textContent = `${scenario.icon} ${scenario.name}`;
                        scenarioPicker.appendChild(option);
                    });
                    scenarioPicker.disabled = false;
                } else {
                    scenarioPicker.innerHTML = '<option value="">该渠道暂无场景</option>';
                    scenarioPicker.disabled = true;
                }
            } else {
                scenarioPicker.disabled = true;
                scenarioPicker.innerHTML = '<option value="">先选择渠道...</option>';
            }
        }

        function addChannelScenario() {
            const channelPicker = document.getElementById('channelPicker');
            const scenarioPicker = document.getElementById('scenarioPicker');
            const selector = document.getElementById('channelScenarioSelector');

            if (channelPicker.value && scenarioPicker.value) {
                const channelText = channelPicker.options[channelPicker.selectedIndex].text;
                const scenarioText = scenarioPicker.options[scenarioPicker.selectedIndex].text;
                const combinedText = `${channelText} / ${scenarioText}`;

                // 检查是否已存在
                const existingItems = Array.from(selector.querySelectorAll('.multi-selector-item'))
                    .map(item => item.textContent.replace('×', '').trim());

                if (existingItems.includes(combinedText)) {
                    alert('该渠道/场景组合已存在');
                    return;
                }

                const item = document.createElement('div');
                item.className = 'multi-selector-item';
                item.innerHTML = `${combinedText} <span class="remove" onclick="removeItem(this)">&times;</span>`;
                item.dataset.channel = channelPicker.value;
                item.dataset.scenario = scenarioPicker.value;
                selector.appendChild(item);

                // 重置选择器
                channelPicker.value = '';
                updateScenarioPicker(channelPicker);
            }
        }

        function addAction(select) {
            if (select.value) {
                const selector = document.getElementById('actionSelector');
                const item = document.createElement('div');
                item.className = 'multi-selector-item';
                item.innerHTML = `${select.options[select.selectedIndex].text} <span class="remove" onclick="removeItem(this)">&times;</span>`;
                selector.appendChild(item);
                select.value = '';
            }
        }

        function removeItem(element) {
            element.parentElement.remove();
        }

        // 阈值配置
        function updateThreshold(slider, type) {
            const input = slider.parentElement.querySelector('.threshold-input');
            input.value = slider.value;
            updatePreview(slider.value, type);
        }

        function updateSlider(input, type) {
            const slider = input.parentElement.querySelector('.threshold-slider');
            slider.value = input.value;
            updatePreview(input.value, type);
        }

        function updatePreview(value, type) {
            const preview = document.querySelector(`[oninput*="${type}"]`).parentElement.querySelector('.threshold-preview');
            let text = '';

            switch (type) {
                case 'liveness':
                    const passRate = Math.round((1 - value) * 100 + 80);
                    const rejectRate = Math.round(value * 5);
                    text = `当前设置：通过率约${passRate}%，误拒率约${rejectRate}%`;
                    break;
                case 'comparison':
                    const compPassRate = Math.round((1 - value) * 100 + 85);
                    const compRejectRate = Math.round(value * 3);
                    text = `当前设置：通过率约${compPassRate}%，误拒率约${compRejectRate}%`;
                    break;
                case 'deepfake':
                    const detectRate = Math.round(value * 100);
                    const falseRate = Math.round((1 - value) * 5);
                    text = `当前设置：检出率约${detectRate}%，误报率约${falseRate}%`;
                    break;
            }

            preview.textContent = text;
        }

        // 开关切换
        function toggleSwitch(element) {
            element.classList.toggle('active');
        }

        // 策略操作
        function editPolicy(policyId) {
            openPolicyModal();
            // 加载策略数据到表单
        }

        function duplicatePolicy(policyId) {
            openPolicyModal();
            // 复制策略数据到表单
        }

        function compareVersion(version) {
            alert(`对比版本 ${version} 功能`);
        }

        function rollbackVersion(version) {
            if (confirm(`确定要回滚到版本 ${version} 吗？`)) {
                alert(`回滚到版本 ${version}`);
                closeVersionModal();
            }
        }

        function saveDraft() {
            alert('策略已保存为草稿');
            closePolicyModal();
        }

        function savePolicy() {
            alert('策略已保存并启用');
            closePolicyModal();
        }

        // 渠道选择事件
        document.addEventListener('click', function (event) {
            if (event.target.classList.contains('channel-option') || event.target.closest('.channel-option')) {
                const channelOption = event.target.classList.contains('channel-option') ?
                    event.target : event.target.closest('.channel-option');
                selectChannel(channelOption);
            }
        });

        // 点击模态框外部关闭
        window.addEventListener('click', function (event) {
            const policyModal = document.getElementById('policyModal');
            const versionModal = document.getElementById('versionModal');
            const channelModal = document.getElementById('channelModal');
            const scenarioModal = document.getElementById('scenarioModal');

            if (event.target === policyModal) {
                closePolicyModal();
            }
            if (event.target === versionModal) {
                closeVersionModal();
            }
            if (event.target === channelModal) {
                closeChannelModal();
            }
            if (event.target === scenarioModal) {
                closeScenarioModal();
            }
        });
    </script>
</body>

</html>